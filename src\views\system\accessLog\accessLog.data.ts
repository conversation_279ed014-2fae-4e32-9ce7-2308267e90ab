import dayjs from "dayjs";

export const columns = [
  {
    title: '系统名称',
    dataIndex: 'appName',
    width: 180,
  },
  {
    title: '菜单名称',
    dataIndex: 'permissionName',
  },
  {
    title: '资源分类',
    dataIndex: 'appClass',
    format: (val) => {
      switch (val) {
        case 1:
          return 'PC';
        case 2:
          return 'APP';
        default:
          return '未知';
      }
    },
  },
  {
    title: '访问人数',
    dataIndex: 'visitUserTotal',
    sorter: {
      multiple: 2,
    },
  },
  {
    title: '访问次数',
    dataIndex: 'visitTotal',
    sorter: {
      multiple: 3,
    },
  },
  {
    title: '查询日期',
    dataIndex: 'date',
    slots: { customRender: 'dateScope' },
    width: 180,
  },
];

export const searchFormSchema = [
  {
    label: '系统名称',
    field: 'appId',
    component: 'Input',
    dataIndex: 'appId_dictText',
    slot: 'appId',
    colProps: { span: 5 },
  },

  {
    label: '菜单名称',
    field: 'permissionName',
    component: 'Input',
    colProps: { span: 5 },
  },
  {
    label: '资源分类',
    field: 'appClass',
    component: 'JDictSelectTag',
    colProps: { span: 5 },
    componentProps: {
      dictCode: 'app_class',
    },
  },
  {
    label: '日期',
    field: 'dateScope',
    component: 'RadioButtonGroup',
    colProps: { span: 10 },
    componentProps: {
      defaultValue: 1,
      //options里面由一个一个的radio组成,支持disabled
      options: [
        { label: '今天', value: 1 },
        { label: '近7天', value: 2 },
        { label: '近30天', value: 3 },
        { label: '近90天', value: 4 },
        { label: '近365天', value: 5 },
        { label: '自定义', value: -1 },
      ],
    },
  },
  {
    label: '日期',
    field: 'setDate',
    component: 'RangeDate',
    colProps: { span: 5 },
    componentProps: {
      //日期格式化
      format: 'YYYY/MM/DD',
      //范围文本描述用集合
      placeholder: ['请选择开始日期', '请选择结束日期'],
      disabledDate: (current) => {
        const oneYearAgo = dayjs().subtract(1, 'year'); // 当前日期前一年
        const oneYearLater = dayjs(); // 当前日期后一年

        return current.isBefore(oneYearAgo) || current.isAfter(oneYearLater);
      },
    },
    ifShow: ({ values }) => {
      return values.dateScope === -1;
    },
  },

  {
    label: '系统名称',
    field: 'appName',
    component: 'Input',
    ifShow: ()=>false,
  },
];
