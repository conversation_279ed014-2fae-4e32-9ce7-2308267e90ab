<template>
    <div style="padding: 12px;">
        <!-- 查询表单 -->
        <div style="display: flex; justify-content: space-between; margin: 10px;">
            <a-form :model="searchForm" layout="inline" style="display: flex; justify-content: space-between;">
                <a-form-item label="工区" :label-col="{ span: 5 }">
                    <a-select v-model:value="searchForm.areaId" placeholder="请选择工区" 
                        style="width: 200px"
                    >
                        <a-select-option v-for="area in areaOptions" :key="area.id" :value="area.id">
                            {{ area.name }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item :label-col="{ span: 20 }">
                    <div style="display: flex; justify-content: between; gap: 12px">
                        <a-button type="primary" @click="handleSearch">查询</a-button>
                        <a-button @click="handleReset">重置</a-button>
                    </div>
                </a-form-item>
            </a-form>
        </div>
    
        <a-table :columns="columns" :data-source="tableData" :pagination="pagination" :row-selection="rowSelection"
            :scroll="{ y: 500 }" :loading="loading" @change="handleTableChange">
            <!-- <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'no'">
                    {{ record.no }}
                </template>
                <template v-if="column.dataIndex === 'id'">
                    {{ record.id }}
                </template>
                <template v-if="column.dataIndex === 'code'">
                    {{ record.code }}
                </template>
                <template v-if="column.dataIndex === 'factoryName'">
                    {{ record.factoryName }}
                </template>
                <template v-if="column.dataIndex === 'workshopName'">
                    {{ record.workshopName }}
                </template>
                <template v-if="column.dataIndex === 'areaName'">
                    {{ areaEnum[record.areaName]?.text }}
                </template>
            </template> -->
        </a-table>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, reactive, onMounted } from 'vue';
import { queryUnusedPots } from '../orgManage.api';

//槽表格显示模式
enum PotTableViewMode {
    ASSIGN,
    VIEW,
}

export default defineComponent({
    props: {
        areaId: {
            type: String,
            required: true,
        },
        viewMode: {
            type: String,
            required: true,
        },
        getSelectedKeys: {
            type: Function,
            required: true,
        },
    },
    setup(props) {
        // 工区列表
        const areaOptions = ref([]);
        const searchForm = reactive<{ sights: any[]; areaId: string }>({
            sights: [],
            areaId: '',
        });
        const tableData = ref<any>([]);
        const areaEnum = ref({});
        const selectedKeys = ref<string[]>([]);
        const loading = ref(false);
        const pagination = reactive({
            current: 1,
            pageSize: 10,
            total: 0,
            showSizeChanger: true,
            onShowSizeChange: (current: number, size: number) => {
                pagination.pageSize = size;
                fetchData();
            },
        });

        const columns = [
            {
                title: '序号',
                dataIndex: 'index',
                key: 'index',
                customRender: ({ index }: { index: number }) => {
                    return index + 1
                },
            },
            {
                title: 'id',
                dataIndex: 'id',
                key: 'id',
                className: 'columnHidden',
                colSpan: 0,
            },
            {
                title: '槽号',
                dataIndex: 'code',
                key: 'code',
                customFilterDropdown: props.viewMode === PotTableViewMode.VIEW ? undefined : false,
            },
            {
                title: '分厂',
                dataIndex: 'factoryName',
                key: 'factoryName',
            },
            {
                title: '车间',
                dataIndex: 'workshopName',
                key: 'workshopName',
            },
            {
                title: '工区',
                dataIndex: 'areaName',
                key: 'areaName',
                customRender: ({ text }: { text: string }) => areaEnum.value[text]?.text,
                customFilterDropdown: props.viewMode === PotTableViewMode.ASSIGN ? undefined : false,
            },
        ];

        const rowSelection = {
            selectedRowKeys: selectedKeys.value,
            onChange: (selectedRowKeys: string[]) => {
                selectedKeys.value = selectedRowKeys;
                props.getSelectedKeys(selectedKeys.value);
            },
        };

        const fetchData = async () => {
            loading.value = true;
            try {
                const response = await queryUnusedPots({ areaId: props.areaId });
                if (response.data) {
                    const tempAreaEnum = {};
                    response.data.forEach((item) => {
                        if (!tempAreaEnum[item.areaName as string]) {
                            tempAreaEnum[item.areaName as string] = {
                                text: item.areaName as string,
                            };
                        }
                    });
                    areaEnum.value = tempAreaEnum;
                }
                tableData.value = response.data
                    .map((item, index) => ({
                        no: String(index + 1),
                        ...item,
                    }));
                pagination.total = response.data.length;
            } finally {
                loading.value = false;
            }
        };


        const handleSearch = () => {

        }

        const handleReset = () => {

        }

        watch(
            () => props.areaId,
            () => {
                fetchData();
            },
            {
                immediate: true,
            }
        );

        watch(
            () => selectedKeys.value,
            () => {
                props.getSelectedKeys(selectedKeys.value);
            }
        );


        return {
            searchForm,
            areaOptions,
            tableData,
            areaEnum,
            selectedKeys,
            loading,
            pagination,
            columns,
            rowSelection,
            handleTableChange: () => { },
            handleSearch,
            handleReset,
        };
    },
});
</script>

<style scoped>
.columnHidden {
    display: none;
}
</style>