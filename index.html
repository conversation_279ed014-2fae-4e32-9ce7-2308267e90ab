<!DOCTYPE html>
<html lang="zh_CN" id="htmlRoot">

  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport"
      content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0" />

    <title>
      <%= title %>
    </title>
    <link rel="manifest" href="/manifest.json">
    <link rel="icon" href="/logo.ico" />
    <!-- 全局配置 -->
    <script>
      window._CONFIG = {};
    </script>
  </head>

  <body>
    <script>
      (() => {
        var htmlRoot = document.getElementById('htmlRoot');
        var theme = window.localStorage.getItem('__APP__DARK__MODE__');
        if (htmlRoot && theme) {
          htmlRoot.setAttribute('data-theme', theme);
          theme = htmlRoot = null;
        }
      })();
    </script>
    <style>
      .skeleton-box {
        position: fixed;
        width: 100%;
        height: 100%;
      }
    </style>
    <div id="app">
      <div class="skeleton-box">
        <img src="/public/skeleton_bg.png" style="width: 100%;margin: 0;"/>
      </div>
    </div>
    <script>
      window.initTime = Date.now();
      window.mylog = console.log;
      window.isReadyOpen = true; //Math.random() > 0.5;
      console.log('performance init time: ', window.initTime);
      console.log('performance isReady Open: ', window.isReadyOpen);
    </script>
    <script type="module" src="/src/main.ts"></script>
    <script>
        // var _hmt = _hmt || [];
        // (function() {
        //     var hm = document.createElement("script");
        //     hm.src = "https://hm.baidu.com/hm.js?0febd9e3cacb3f627ddac64d52caac39";
        //     var s = document.getElementsByTagName("script")[0];
        //     s.parentNode.insertBefore(hm, s);
        // })();
    </script>
  </body>
</html>
