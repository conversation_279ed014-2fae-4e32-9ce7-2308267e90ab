/*
 * @Description:
 * @Autor: yst
 * @Date: 2024-10-22 15:51:49
 * @LastEditors: yst
 * @LastEditTime: 2024-10-24 08:19:10
 */
import { defHttp } from '/@/utils/http/axios';

export enum Api {
  list = '/sys/opinion/list',
  exportXls = '/sys/opinion/exportXls',
  getCurrentUserDeparts = '/sys/user/getCurrentUserDeparts',
  userDepartList = '/sys/user/userDepartList',
}
export const queryDepartAppPermission = (params?) => defHttp.get({ url: Api.queryDepartAppPermission, params });
export const list = (params?) => defHttp.get({ url: Api.list, params });
//导出
export const exportXlsApi = Api.exportXls;
export const getUserDepartList = (params) => defHttp.get({ url: Api.userDepartList, params }, { successMessageMode: 'none' });
export const getUserDeparts = (params?) => defHttp.get({ url: Api.getCurrentUserDeparts, params });
