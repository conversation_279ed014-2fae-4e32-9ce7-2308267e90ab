# 是否启用mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /system

# 是否启用gzip或brotli压缩
# 选项值: gzip | brotli | none
# 如果需要多个可以使用“,”分隔
VITE_BUILD_COMPRESS = 'gzip'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

#后台接口父地址(必填)
VITE_GLOB_API_URL=/system/api

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=//ipms.test.hongqiaocloud.com/system/api

# 单点登录服务端地址
VITE_GLOB_APP_CAS_BASE_URL=//ipms.test.hongqiaocloud.com/cas

# 文件预览地址
VITE_GLOB_ONLINE_VIEW_URL=//fileview.jeecg.com/onlinePreview

# 接口父路径前缀
VITE_GLOB_API_URL_PREFIX=

# 是否启用图像压缩
VITE_USE_IMAGEMIN= true

# 是否兼容旧浏览器
VITE_LEGACY = false
