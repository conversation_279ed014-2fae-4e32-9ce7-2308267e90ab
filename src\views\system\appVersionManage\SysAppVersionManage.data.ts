import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { queryMyAppAll } from '/@/api/system/app.api';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '应用名称',
    align: 'center',
    dataIndex: 'appName',
  },
  {
    title: '版本名称',
    align: 'center',
    dataIndex: 'appVersion',
    customRender: ({ text }) => {
      return `V${text}`;
    },
  },
  {
    title: '版本号',
    align: 'center',
    dataIndex: 'versionNum',
    // customRender: ({ text }) => {
    //   return `V${text}`;
    // },
  },
  // {
  //   title: '部门ID',
  //   align: 'center',
  //   dataIndex: 'departId',
  // },
  {
    title: '更新描述',
    align: 'center',
    dataIndex: 'updateDesc',
  },
  {
    title: '版本文件',
    align: 'center',
    dataIndex: 'versionFile',
  },
  {
    title: '更新类型',
    align: 'center',
    dataIndex: 'updateType',
    customRender: ({ text }) => {
      return text === 0 ? '推荐更新' : '强制更新';
    },
  },
  // {
  //   title: '二维码',
  //   align: 'center',
  //   dataIndex: 'qrCode',
  // },
  {
    title: '上架状态',
    align: 'center',
    dataIndex: 'appReleaseFlag',
    customRender: ({ text }) => {
      return text === 'Y' ? '上架' : '下架';
    },
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    field: 'appName',
    label: '应用名称',
    component: 'Input',
  },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'JDictSelectTag',
  //   componentProps: {
  //     dicCode: 'status',
  //   },
  // },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '应用名称',
    field: 'appId',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: queryMyAppAll,
      params: {
        appClass: '2',
      },
      labelField: 'appName',
      valueField: 'id',
    },
  },
  {
    label: '版本名称',
    field: 'appVersion',
    component: 'Input',
    required: true,
    renderComponentContent: () => {
      return {
        prefix: () => 'V',
      };
    },
  },
  {
    label: '版本号',
    field: 'versionNum',
    component: 'Input',
    required: true,
  },
  {
    label: '上架状态',
    field: 'appReleaseFlag',
    component: 'RadioGroup',
    required: true,
    defaultValue: 'N',
    componentProps: {
      options: [
        { label: '上架', value: 'Y' },
        { label: '下架', value: 'N' },
      ],
    },
  },
  // {
  //   label: '部门ID',
  //   field: 'departId',
  //   component: 'RadioGroup',

  //   required: true,
  //   componentProps: {
  //     options: [
  //       { label: '铝业一公司', value: '1' },
  //       { label: '铝业二公司', value: '2' },
  //     ],
  //   },
  // },
  {
    label: '更新描述',
    field: 'updateDesc',
    component: 'InputTextArea',
  },
  {
    label: '版本文件',
    field: 'versionFile',
    component: 'JUpload',
    rules: [{ required: true, message: '请选择上传文件' }],
    componentProps: ({ formModel }) => {
      return {
        text: '文件上传',
        fileType: 'application/vnd.android.package-archive',
        onChange: ({ value }) => {
          console.log(formModel, '监听图片地址变化');
          // formModel.versionFile;
        },
        beforeUpload: ({ values }) => {
          debugger;
          console.log(formModel, values, 'values');
        },
      };
    },

    dynamicDisabled: ({ values }) => {
      return !!values.appVersion ? false : true;
    },
  },
  {
    label: '更新类型',
    field: 'updateType',
    component: 'RadioGroup',
    required: true,
    defaultValue: 0,
    componentProps: {
      options: [
        { label: '推荐更新', value: 0 },
        { label: '强制更新', value: 1 },
      ],
    },
  },
  // {
  //   label: '二维码',
  //   field: 'qrCode',
  //   component: 'Input',
  // },
  // {
  //   label: '状态',
  //   field: 'status',
  //   component: 'InputNumber',
  // },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
