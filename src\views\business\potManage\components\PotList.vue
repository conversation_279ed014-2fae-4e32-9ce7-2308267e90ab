<template>
    <div style="padding: 12px;">
        <!-- 查询表单 -->
        <div style="display: flex; justify-content: space-between; margin: 10px;">
            <a-form ref="formRef" :model="searchForm" layout="inline"
                style="display: flex; justify-content: space-between;">
                <a-form-item label="槽号" name="potCode" :label-col="{ span: 5 }">
                    <a-input v-model:value="searchForm.potCode" />
                </a-form-item>
                <!-- <a-form-item label="操作" name="opera" :label-col="{ span: 5 }">
                    <a-input v-model:value="searchForm.opera" />
                </a-form-item> -->
                <a-form-item :label-col="{ span: 20 }">
                    <div style="display: flex; justify-content: between; gap: 12px">
                        <a-button type="primary" @click="handlePointConfig">点位配置</a-button>
                        <a-button type="primary" @click="handleSearch">查询</a-button>
                        <a-button @click="handleReset">重置</a-button>
                    </div>
                </a-form-item>
            </a-form>
        </div>

        <a-table :columns="columns" :data-source="filteredData" :pagination="pagination" :scroll="{ x: 'max-content', y: 500 }"
            :loading="loading" @change="handleTableChange">
            <template #bodyCell="{ column, record }">
                <template v-if="!record[column.dataIndex] && column.dataIndex !== 'opera' && column.dataIndex !== 'no'">
                    -
                </template>
                <!-- <template v-if="column.dataIndex === 'no'">
                    {{ record.no }}
                </template> -->
                <!-- <template v-if="column.dataIndex === 'id'">
                                    {{ record.id }}
                                </template>
                <template v-if="column.dataIndex === 'code'">
                                    {{ record.code }}
                                </template>
                <template v-if="column.dataIndex === 'factoryName'">
                                    {{ record.factoryName }}
                                </template>
                <template v-if="column.dataIndex === 'workshopName'">
                                    {{ record.workshopName }}
                                </template> -->
                <template v-if="column.dataIndex === 'opera'">
                    <a @click="onChangeType(record)">
                        修改类型
                    </a>
                </template>
            </template>
        </a-table>
        <!-- 修改类型弹窗 -->
        <a-modal title="修改类型" v-model:visible="modifyType" width="40vw" ok-text="确定修改" @ok="handleModifyTypeOk"
            @cancel="handleModifyTypeCancel">
            <a-form ref="modifyTypeForm" 
                :model="modifyTypeFormData" 
                :label-col="{ span: 6 }"
                :initial-values="{ remember: true }" 
                auto-complete="off"
                style="margin: 20px;"
            >
                <a-form-item label="槽类型" name="potTypeId" :rules="[{ required: true, message: '请输入槽类型' }]">
                    <a-select v-model:value="modifyTypeFormData.potTypeId" :options="slotTypeArray" />
                </a-form-item>
            </a-form>
        </a-modal>
        <a-modal v-model:visible="visible" width="900px" title="点位配置" @ok="handleOk" @cancel="handleCancel" :confirmLoading="confirmLoading">
            <div style="margin: 20px;">
                <a-form ref="pointForm" 
                    :model="pointFormData" 
                    :label-col="{ span: 3 }"
                    :initial-values="{ remember: true }" 
                    auto-complete="off"
                >
                <a-row>
                    <a-col :span="12">
                        <a-form-item 
                            label="A钢棒" 
                            name="potStA"
                            :labelCol="{ span: 6, }" 
                            :rules="[{ required: true, message: '请输入' }]"
                        >
                            <a-input-number style="width: 150px;" v-model:value="pointFormData.potStA" placeholder="请输入" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item 
                            label="B钢棒" 
                            name="potStB"
                            :labelCol="{ span:6, }" 
                            :rules="[{ required: true, message: '请输入' }]"
                        >
                            <a-input-number style="width: 150px;" v-model:value="pointFormData.potStB" placeholder="请输入" />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="12">
                        <a-form-item 
                            label="A侧壁" 
                            name="potSwA"
                            :labelCol="{ span: 6, }" 
                            :rules="[{ required: true, message: '请输入' }]"
                        >
                            <a-input-number style="width: 150px;" v-model:value="pointFormData.potSwA" placeholder="请输入" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item 
                            label="B侧壁" 
                            name="potSwB"
                            :labelCol="{ span: 6, }" 
                            :rules="[{ required: true, message: '请输入' }]"
                        >
                            <a-input-number style="width: 150px;" v-model:value="pointFormData.potSwB" placeholder="请输入" />
                        </a-form-item>
                    </a-col>
                </a-row>
                    <a-form-item 
                        label="批量生成工区" 
                        name="areaIds" 
                        :labelCol="{ span: 3, }" 
                        :rules="[{ required: true, message: '请选择' }]"
                    >
                        <a-tree-select
                            v-model:value="pointFormData.areaIds"
                            style="width: 100%"
                            :tree-data="areaData"
                            tree-checkable
                            allow-clear
                            :show-checked-strategy="SHOW_ALL"
                            placeholder="请选择工区"
                            tree-node-filter-prop="label"
                            :field-names="{
                                children: 'childs',
                                label: 'name',
                                value: 'id',
                            }"
                        />
                    </a-form-item>
                </a-form>
            </div>
          </a-modal>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, reactive, onMounted, h, computed } from 'vue';
import { queryPotsById, queryDict, updateType, queryOrg, savePotPoint } from '../potManage.api';
import type { FormInstance } from 'ant-design-vue';
import { message, TreeSelect } from 'ant-design-vue'
import { OrgType } from '../potManage.data';
const SHOW_ALL = TreeSelect.SHOW_ALL;

//槽表格显示模式
enum PotTableViewMode {
    ASSIGN,
    VIEW,
}

export default defineComponent({
    props: {
        areaId: {
            type: String,
            required: true,
        },
        viewMode: {
            type: String,
            required: true,
        },
        getSelectedKeys: {
            type: Function,
            required: true,
        },
    },
    setup(props) {
        // 工区列表
        const areaOptions = ref([]);
        const formRef = ref<FormInstance>();
        const searchForm = reactive<{ potCode: string; opera: string }>({
            potCode: '',
            opera: '',
        });
        const searchKey = ref<string>('')
        const tableData = ref<any>([]);
        const areaEnum = ref({});
        const selectedKeys = ref<string[]>([]);
        const loading = ref(false);
        const pagination = reactive({
            current: 1,
            pageSize: 10,
            total: 0,
            showSizeChanger: true,
            onShowSizeChange: (current: number, size: number) => {
                pagination.pageSize = size;
                // fetchData();
            },
            onChange: (page, size) => {
                pagination.current = page;
            }
        });

        const columns = [
            {
                title: '序号',
                dataIndex: 'no',
                key: 'no',
                width: 50,
                customRender: ({ index }: { index: number }) => {
                    return index + 1
                },
            },
            // {
            //     title: 'id',
            //     dataIndex: 'id',
            //     // key: 'id',
            //     className: 'columnHidden',
            // },
            {
                title: '槽号',
                dataIndex: 'code',
                // key: 'code',
                filterSearch: true,
                width: 50,
                onFilter: () => {
                    console.log('filter:')
                }
            },
            {
                title: '分厂',
                dataIndex: 'factoryName',
                // key: 'factoryName',
                width: 150,
            },
            {
                title: '车间',
                dataIndex: 'workshopName',
                // key: 'workshopName',
                width: 60,
            },
            {
                title: '状态',
                dataIndex: 'statusName',
                // key: 'statusName',
                width: 60,
            },
            {
                title: '最近停槽时间',
                dataIndex: 'stopDate',
                // key: 'stopDate',
                width: 120,
            },
            {
                title: '最近通电时间',
                dataIndex: 'energizeDate',
                // key: 'energizeDate',
                width: 120,
            },
            {
                title: '最近生产时间',
                dataIndex: 'produceDate',
                // key: 'produceDate',
                width: 120,
            },
            {
                title: '槽龄',
                dataIndex: 'potAge',
                // key: 'potAge',
                width: 60,
            },
            {
                title: '槽代数',
                dataIndex: 'era',
                // key: 'era',
                width: 90,
            },
            {
                title: '槽类型',
                dataIndex: 'potTypeName',
                // key: 'potTypeName',
                width: 90,
            },
            {
                title: '工区',
                dataIndex: 'areaName',
                // key: 'areaName',
                width: 60,
            },
            {
                title: '操作',
                fixed: 'right',
                dataIndex: 'opera',
                bodyCell: 'opera',
                width: 70,
            }
        ];

        const rowSelection = {
            selectedRowKeys: selectedKeys.value,
            onChange: (selectedRowKeys: string[]) => {
                selectedKeys.value = selectedRowKeys;
                props.getSelectedKeys(selectedKeys.value);
            },
        };

        const fetchData = async () => {
            loading.value = true;
            try {
                const response = await queryPotsById({ queryId: props.areaId });
                tableData.value = response.data
                pagination.total = response.data.length;
            } finally {
                loading.value = false;
            }
        };

        const handleSearch = () => {
            console.log('searchForm:', searchForm)
            searchKey.value = searchForm.potCode
        }

        const handleReset = () => {
            formRef.value?.resetFields();
            searchKey.value = ''
        }

        const handlePointConfig = () => {
            queryOrg().then(res => {
                const handleDisabled = (data) => {
                    const temp = data.map(item => {
                        item.checkable = item.typeId === OrgType.AREA
                        if (item.childs?.length) {
                            item.childs = handleDisabled(item.childs)
                        }
                        return item
                    })
                    return temp
                }

                areaData.value = handleDisabled(res);
                console.log('areaData.value:', areaData.value)
            })
            visible.value = true
        };

        const filteredData = computed(() => {
            if (!searchKey.value) return tableData.value;
            
            const res = tableData.value.filter(item =>
                item.code.toLowerCase().includes(searchKey.value.toLowerCase())
            );

            // 重置页数为1
            pagination.current = 1;
            pagination.total = res.length || 0

            return res
        });

        // 点位配置
        const pointForm = ref(null);
        const pointFormData = ref({
            "areaIds": [],
            "potStA": 28,
            "potStB": 28,
            "potSwA": 28,
            "potSwB": 28
        });
        const areaData = ref([]); 
        const confirmLoading = ref(false)

        // 修改类型
        const modifyType = ref(false);
        const modifyTypeForm = ref(null);
        const modifyTypeFormData = ref({
            potTypeId: '',
        });
        const slotTypeArray = ref([]);
        const curRecord = ref({});
        const onChangeType = (record) => {
            curRecord.value = record;
            queryDict({ dictCode: 'potType' }).then(res => {
                slotTypeArray.value = res.map(r => ({
                    label: r.value,
                    value: r.id
                }))
                modifyTypeFormData.value.potTypeId = record?.potTypeId;
            })
            modifyType.value = true;
        }

        const handleModifyType = (record) => {
            // listUsingGET1({ dictCode: 'potType' }).then((res) => {
            //     slotTypeArray.value = res?.data?.map((item) => ({
            //         label: item.value,
            //         value: item.id,
            //     }));
            //     modifyTypeFormData.value.potTypeId = record?.potTypeId;
            // });
            modifyType.value = true;
        };

        const handleModifyTypeOk = () => {
            modifyTypeForm.value?.validate().then(() => {
                updateType({
                    id: curRecord.value?.id,
                    ...modifyTypeFormData.value,
                }).then((res) => {
                    message.info('修改成功');
                    // 刷新表格数据
                    // ref.current?.reload();
                    fetchData()
                });
                modifyType.value = false;
            });
        };

        const handleModifyTypeCancel = () => {
            modifyType.value = false;
        };

        const visible = ref<boolean>(false)

        const handleOk = () => {
            confirmLoading.value = true
            pointForm.value?.validate().then(() => {
                savePotPoint(pointFormData.value).then(res => {
                    pointForm.value?.resetFields()
                    visible.value = false
                    // fetchData()
                }).finally(() => {
                    confirmLoading.value = false
                });
            });
        }

        const handleCancel = () => {
            pointForm.value?.resetFields()
            visible.value = false
        }

        watch(
            () => props.areaId,
            () => {
                fetchData();
            }
        );

        watch(
            () => selectedKeys.value,
            () => {
                props.getSelectedKeys(selectedKeys.value);
            }
        );


        return {
            formRef,
            searchForm,
            filteredData,
            areaOptions,
            tableData,
            areaEnum,
            selectedKeys,
            loading,
            pagination,
            columns,
            rowSelection,
            pointForm,
            pointFormData,
            SHOW_ALL,
            areaData,
            modifyType,
            modifyTypeForm,
            modifyTypeFormData,
            slotTypeArray,
            handleModifyType,
            handleModifyTypeOk,
            handleModifyTypeCancel,
            visible,
            confirmLoading,
            handleOk,
            handleCancel,
            fetchData,
            onChangeType,
            handleTableChange: () => { },
            handleSearch,
            handleReset,
            handlePointConfig,
        };
    },
});
</script>

<style scoped>
.columnHidden {
    display: none;
}
</style>