import 'uno.css';
import '/@/design/index.less';
// 注册图标
import 'virtual:svg-icons-register';
import AppCom from './App.vue';
import { createApp, App } from 'vue';
import { initAppConfigStore } from '/@/logics/initAppConfig';
import { setupErrorHandle } from '/@/logics/error-handle';
import { router, setupRouter, resetRouter } from '/@/router';
import { setupRouterGuard } from '/@/router/guard';
import { setupStore } from '/@/store';
import { setupGlobDirectives } from '/@/directives';
import { setupI18n } from '/@/locales/setupI18n';
import { registerGlobComp } from '/@/components/registerGlobComp';
import { registerThirdComp } from '/@/settings/registerThirdComp';
import { useSso } from '/@/hooks/web/useSso';
// 注册online模块lib
import { registerPackages } from '/@/utils/monorepo/registerPackages';

// qiankun
// import { renderWithQiankun, qiankunWindow } from 'vite-plugin-qiankun/dist/helper';
import { useUserStore } from '/@/store/modules/user';

(window as any).mylog('performance main import over: ', Date.now() - (window as any).initTime);

// 在本地开发中引入的,以提高浏览器响应速度
if (import.meta.env.DEV) {
  import('ant-design-vue/dist/antd.less');
}

/** wujie */
declare global {
  interface Window {
    // 是否存在无界
    __POWERED_BY_WUJIE__?: boolean;
    // 子应用mount函数
    __WUJIE_MOUNT: () => void;
    // 子应用unmount函数
    __WUJIE_UNMOUNT: () => void;
    // 子应用无界实例
    __WUJIE: { mount: () => void };
  }
}

let app: App<Element>;
async function render(props = {}) {

  (window as any).mylog('performance bootstrap start: ', Date.now() - (window as any).initTime);

  // 创建应用实例
  app = createApp(AppCom);

  // 多语言配置,异步情况:语言文件可以从服务器端获得
  await setupI18n(app);

  // 配置存储
  setupStore(app);

  // 初始化内部系统配置
  initAppConfigStore();

  // 注册外部模块路由(注册online模块lib)
  registerPackages(app);

  // 注册全局组件
  registerGlobComp(app);

  (window as any).mylog('performance register glob over: ', Date.now() - (window as any).initTime);

  // qiankun
  // if (qiankunWindow.__POWERED_BY_QIANKUN__) {
  // wujie
  if (window.__POWERED_BY_WUJIE__) {
    const { data } = props as any;
    const userStore = useUserStore();
    userStore.setToken(data.token);
  }

  //CAS单点登录
  await useSso().ssoLogin();

  // 配置路由
  setupRouter(app);

  // 路由保护
  setupRouterGuard(router);

  (window as any).mylog('performance router over: ', Date.now() - (window as any).initTime);

  // 注册全局指令
  setupGlobDirectives(app);

  // 配置全局错误处理
  setupErrorHandle(app);

  (window as any).mylog('performance register Super over: ', Date.now() - (window as any).initTime);

  // 注册第三方组件
  await registerThirdComp(app);

  (window as any).mylog('performance register Third over: ', Date.now() - (window as any).initTime);

  // 当路由准备好时再执行挂载( https://next.router.vuejs.org/api/#isready)
  await router.isReady();

  // 挂载应用
  const { container } = props as any;

  (window as any).mylog('performance bootstrap in before mount: ', Date.now() - (window as any).initTime);

  app.mount(container ? container.querySelector('#app') : '#app', true);

  (window as any).mylog('performance bootstrap end: ', Date.now() - (window as any).initTime);
  (window as any).mylog('performance test cache3: ', Date.now() - (window as any).initTime);

	const parentProps = window.$wujie?.props;
	window.$wujie?.bus.$on(`${parentProps.name}-routeChange`,(path) => {
	router.push(path);
	});

}

/** qiankun */
// qiankun 微前端钩子函数
// renderWithQiankun({
//   mount(props) {
//     console.log('viteapp mount')
//     render(props);
//   },
//   bootstrap() {
//     console.log('bootstrap')
//   },
//   unmount(props) {
//     console.log('vite被卸载了')
//     app.unmount()
//     app._container.innerHTML = ''
//     resetRouter();
//   },
//   update(props) {
//     console.log('vite更新');
//   },
// });

// if (!qiankunWindow.__POWERED_BY_QIANKUN__) {
//   render({})
// }

/** wujie */
if (window.__POWERED_BY_WUJIE__) {
  window.__WUJIE_MOUNT = () => {
    console.log('子应用挂载');
    const props = window.$wujie?.props;
    render(props);
  };
  window.__WUJIE_UNMOUNT = () => {
    console.log('子应用卸载');
    app.unmount();
  };
  // module脚本异步加载，应用主动调用生命周期
  window.__WUJIE.mount();
} else {
  render({});
}
