<template>
    <a-row :class="['p-4', `${prefixCls}--box`]" type="flex" :gutter="10">
        <a-col :xl="6" :lg="12" :md="12" style="margin-bottom: 10px">
            <OrgTree ref="leftTree" @select="onTreeSelect" @rootTreeData="onRootTreeData" />
        </a-col>
        <a-col :xl="18" :lg="36" :md="36" style="margin-bottom: 10px">
            <div style="height: 100%" :class="[`${prefixCls}`]">
                <OrgForm 
                    :data="departData" 
                    :rootTreeData="rootTreeData"
                    :displayMode="displayMode"
                    @success="onSuccess" 
                />
            </div>
        </a-col>
    </a-row>
</template>
<script lang="ts" setup>
import { provide, ref, onMounted } from 'vue';
import { useDesign } from '/@/hooks/web/useDesign';
import OrgTree from './components/OrgTree.vue';
import OrgForm from './components/OrgForm.vue';
import { DisplayMode } from '@/views/business/orgManage/orgManage.data';

// import {}
const { prefixCls } = useDesign('org-manage');
provide('prefixCls', prefixCls);

// 给子组件定义一个ref变量
const leftTree = ref();

// 当前选中的部门信息
const departData = ref({});
const rootTreeData = ref<any[]>([]);
const displayMode = ref<DisplayMode>(DisplayMode.DEFAULT);

// 左侧树选择后触发
function onTreeSelect(data, displayM: DisplayMode) {
    departData.value = data;
    displayMode.value = displayM
    console.log('displayMode:', data, displayM)
}

// 左侧树rootTreeData触发
function onRootTreeData(data) {
    rootTreeData.value = data;
}

function onSuccess() {
    // leftTree.value.loadRootTreeData();
    console.log('leftTree.value', leftTree.value)
}

// onMounted(() => {
//     leftTree.value.loadRootTreeData();
// })
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-org-manage';

.@{prefix-cls} {
    // update-begin-author:liusq date:20230625 for: [issues/563]暗色主题部分失效
    background: @component-background;
    // update-end-author:liusq date:20230625 for: [issues/563]暗色主题部分失效

    &--box {
        .ant-tabs-nav {
            padding: 0 20px;
        }
    }
}
</style>