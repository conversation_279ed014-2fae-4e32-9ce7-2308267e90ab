import { unref } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

export enum Api {
  // org
  queryOrg = '/orgs/0/childs',
  queryOrgType = '/dicts/getOrgType',
  queryDict = '/dicts',
  queryUsedPots = '/pots/getUsedPots',
  queryUnusedPots = '/pots/getUnusedPots',
  queryUserDepTrees = '/middleSystemDepartment/getUserDepTrees',
  saveOrgs = '/orgs', // 新增/修改 /删除
}

/**
 * 获取组织列表
 */
export const queryOrg = (params?) => defHttp.get({ url: Api.queryOrg, params });

/**
 * 获取组织类型
 */
export const queryOrgType = (params?) => defHttp.get({ url: Api.queryOrgType, params });

/**
 * 获取字典类型
 */
export const queryDict = (params?) => defHttp.get({ url: Api.queryDict, params });

/**
 * 获取已使用槽
 */
export const queryUsedPots = (params?) => defHttp.get({ url: Api.queryUsedPots, params });

/**
 * 获取未使用槽
 */
export const queryUnusedPots = (params?) => defHttp.get({ url: Api.queryUnusedPots, params });

/**
 * 获取组织绑定列表
 */
export const queryUserDepTrees = (params?) => defHttp.post({ url: Api.queryUserDepTrees, params });

/**
 * 新增或者修改
 */
export const saveOrgs = (params?) => defHttp.post({ url: Api.saveOrgs, params });

/**
 * 新增或者修改
 */
export const deleteOrgs = (params?) => defHttp.delete({ url: Api.saveOrgs, params });

