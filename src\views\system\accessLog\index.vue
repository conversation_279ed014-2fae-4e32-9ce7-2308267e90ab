<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { BasicTable, TableAction } from '@/components/Table';
  import { list, appList } from './index.api';
  import { columns, searchFormSchema } from './accessLog.data';
  import { useListPage } from '@/hooks/system/useListPage';
  import detatilModal from './detatilModal.vue';
  import dayjs from 'dayjs';
  import { useModal } from '/@/components/Modal';

  const [register, { openModal }] = useModal();
  const dateScope = ref('');

  const { tableContext } = useListPage({
    designScope: 'access-log-list',
    tableProps: {
      api: list,
      columns: columns,
      size: 'small',
      immediate: false,
      showTableSetting: false,
      formConfig: {
        labelWidth: '80px',
        schemas: searchFormSchema,
        resetFunc: () => {
          appName = '';
          getForm().setFieldsValue({
            dateScope: 1,
          });
        },
      },
      actionColumn: {
        width: 240,
      },
      defSort: false,
      beforeFetch: (params) => {
        if (params.appId) {
          params.appName = appOptions.value.find((item) => {
            return item.value == params.appId;
          }).label;
        }

        if (params.dateScope) {
          params = formatDateScope(params);

          delete params.dateScope;
        }

        if (params.column || params.sortInfoString) {
          const order = {};

          let sortData = [];
          if (params.sortInfoString) {
            sortData = JSON.parse(params.sortInfoString);
            delete params.sortInfoString;
          }

          if (sortData.length > 1) {
            sortData.forEach((item) => {
              order[item.column] = item.order == 'asc' ? 'true' : 'false';
            });
          } else {
            if (params.column !== 'createTime') {
              order[params.column] = params.order == 'asc' ? 'true' : 'false';
            }
          }

          params.orders = order;
          delete params.order;
        }

        delete params.column;
      },
    },
  });

  function formatDateScope(params) {
    if (params.dateScope === 1) {
      params.startTime = dayjs().startOf('day').format('YYYY-MM-DD');
      params.endTime = dayjs().endOf('day').format('YYYY-MM-DD');
    } else if (params.dateScope === 2) {
      params.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
      params.endTime = dayjs().endOf('day').format('YYYY-MM-DD');
    } else if (params.dateScope === 3) {
      params.startTime = dayjs().subtract(30, 'day').format('YYYY-MM-DD');
      params.endTime = dayjs().endOf('day').format('YYYY-MM-DD');
    } else if (params.dateScope === 4) {
      params.startTime = dayjs().subtract(90, 'day').format('YYYY-MM-DD');
      params.endTime = dayjs().endOf('day').format('YYYY-MM-DD');
    } else if (params.dateScope === 5) {
      params.startTime = dayjs().subtract(365, 'day').format('YYYY-MM-DD');
      params.endTime = dayjs().endOf('day').format('YYYY-MM-DD');
    } else if (params.dateScope === -1 && params.setDate) {
      params.startTime = params.setDate.split(',')[0];
      params.endTime = params.setDate.split(',')[1];

      delete params.setDate;
    } else if (params.dateScope === -1) {
      params.startTime = dayjs().startOf('day').format('YYYY-MM-DD');
      params.endTime = dayjs().endOf('day').format('YYYY-MM-DD');
    }
    dateScope.value = `${params.startTime} - ${params.endTime}`;
    return params;
  }

  const [registerTable, { getForm, reload }] = tableContext;

  function getTableAction(record) {
    const dateScopeData = formatDateScope(getForm().getFieldsValue());

    return [
      {
        label: '访问明细',
        onClick: handleDetail.bind(null, {
          record,
          dateScopeData,
        }),
      },
      // {
      //   label: '未访问明细',
      //   onClick: handleDetail.bind(null, {
      //     record,
      //     dateScopeData,
      //   }, false),
      // },
    ];
  }

  onMounted(() => {
    initTableFormData();
  });
  const appOptions = ref([]);
  let appName = '';

  function initTableFormData() {
    appList().then((res) => {
      appOptions.value = res;
    });
    getForm()
      .setFieldsValue({
        dateScope: 1,
      })
      .then(() => {
        reload();
      });
  }

  function handleAppChange(e) {
    const data = appOptions.value.find((item) => {
      return item.value == e;
    });
    if (data) {
      appName = data.label;
    }
  }

  function getDropDownAction(record) {
    return [];
  }

  function handleDetail(data, isVisit = true) {
    openModal(true, {
      ...data,
      isVisit,
    });
  }
</script>

<template>
  <BasicTable @register="registerTable">
    <template #dateScope>
      {{ dateScope }}
    </template>
    <template #form-appId="{ model, field }">
      <a-select ref="select" placeholder="请选择系统" v-model:value="model[field]" :options="appOptions" @change="handleAppChange" :allowClear="true"></a-select>
    </template>
    <template #action="{ record }">
      <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
    </template>
  </BasicTable>
  <detatilModal @register="register" />
</template>

<style scoped lang="less"></style>
