import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

// 是否接入开放平台
const ifOPS = (type) => type === 'E';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '应用名称',
    align: 'center',
    dataIndex: 'appName',
    width: 300,
  },
  {
    title: '应用标识',
    align: 'center',
    sorter: true,
    dataIndex: 'appCode',
    width: 150,
  },
  {
    title: '应用分类',
    align: 'center',
    sorter: true,
    dataIndex: 'appClass',
    width: 100,
    customRender({ record }) {
      return record.appClass_dictText;
    },
  },
  {
    title: '应用分组',
    align: 'center',
    sorter: true,
    dataIndex: 'appGroup',
    width: 100,
    customRender({ text }) {
      return render.renderDict(text, 'app_group');
    },
  },
  {
    title: '应用类型',
    align: 'center',
    sorter: true,
    dataIndex: 'appType',
    width: 100,
    customRender({ record }) {
      return record.appType_dictText;
    },
  },
  {
    title: '应用路由',
    align: 'center',
    sorter: true,
    dataIndex: 'appRoute',
  },
  {
    title: '应用LOGO',
    align: 'center',
    dataIndex: 'appLogo',
    customRender: (row) => render.renderListImage(row, 50),
  },
  {
    title: '应用版本',
    align: 'center',
    dataIndex: 'appVersion',
  },
  {
    title: '排序',
    align: 'center',
    sorter: true,
    width: 80,
    dataIndex: 'sortOrder',
  },
  {
    title: '上架标识',
    align: 'center',
    dataIndex: 'appReleaseFlag',
    width: 100,
    customRender({ record }) {
      return record.appReleaseFlag_dictText;
    },
  },
  {
    title: '上架时间',
    align: 'center',
    sorter: true,
    dataIndex: 'appReleaseTime',
  },
  {
    title: '说明',
    align: 'center',
    dataIndex: 'description',
    width: 400,
    slots: { customRender: 'htmlSlot' },
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '应用名称',
    field: 'appName',
    component: 'JInput', //TODO 范围查询
    colProps: { span: 6 },
  },
  {
    label: '应用标识',
    field: 'appCode',
    component: 'JInput', //TODO 范围查询
    colProps: { span: 6 },
  },
  {
    label: '应用分类',
    field: 'appClass',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'app_class',
    },
    colProps: { span: 6 },
  },
  {
    label: '应用分组',
    field: 'appGroup',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'app_group',
    },
    colProps: { span: 6 },
  },
  {
    label: '应用类型',
    field: 'appType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'app_type',
    },
    colProps: { span: 6 },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '应用名称',
    field: 'appName',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入应用名称!', trigger: 'blur' }];
    },
  },
  {
    label: '应用标识',
    field: 'appCode',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入应用标识!' }, { ...rules.duplicateCheckRule('sys_app', 'app_code', model, schema)[0] }];
    },
  },
  {
    label: '应用分类',
    field: 'appClass',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'app_class',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择应用分类!' }];
    },
  },
  {
    label: '应用类型',
    field: 'appType',
    component: 'JDictSelectTag',

    componentProps: {
      dictCode: 'app_type',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择应用类型!' }];
    },
  },
  {
    label: '接入开放平台',
    field: 'ifOPS',
    component: 'Switch',
    defaultValue: 0,
    componentProps: {
      checkedChildren: '是',
      unCheckedChildren: '否',
    },
    ifShow: ({ values }) => ifOPS(values.appType),
  },
  {
    label: '门户分组',
    field: 'appGroup',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'app_group',
      stringToNumber: true,
    },
  },
  {
    label: '应用路由',
    field: 'appRoute',
    component: 'Input',
  },
  {
    label: '应用图标',
    field: 'appIcon',
    component: 'IconPicker',
  },
  {
    label: '应用LOGO',
    field: 'appLogo',
    component: 'JImageUpload',
    componentProps: {
      bizPath: '/logo/sys/app',
    },
  },
  {
    label: '应用版本',
    field: 'appVersion',
    component: 'Input',
  },
  {
    field: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    defaultValue: 1,
  },
  {
    label: '上架标识',
    field: 'appReleaseFlag',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'release_flag',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择上架标识!' }];
    },
  },
  {
    label: '上架时间',
    field: 'appReleaseTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '说明',
    field: 'description',
    component: 'InputTextArea',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
