<template>
    <a-card :bordered="false" style="height: 100%">
        <a-spin :spinning="loading">
            <BasicForm @register="registerForm" />
            <!-- 槽号 -->
            <a-row :gutter="[10, 32]" v-if="displayMode === DisplayMode.AREA && !!props.data.id" align="middle">
                <a-col :span="4" style="text-align: right;">
                    <label>槽号:</label>
                </a-col>
                <a-button @click="onSelectPots">选择槽号</a-button>
            </a-row>

            <a-row :gutter="[10, 32]" v-if="displayMode === DisplayMode.AREA && !!props.data.id"
                style="margin-top: 20px;">
                <a-col :offset="4" :span="20">
                    <tank-list :tank-list-data="usedPotsList" />
                </a-col>
            </a-row>

            <!-- 按钮组 -->
            <!-- <a-row style="margin-top: 20px;">
                <a-col :offset="4" :span="20">
                    <a-space>
                        <a-button id="edit" :disabled="canEdit" @click="handleEditClick" type="primary">
                            编辑
                        </a-button>
                        <a-button :disabled="!canEdit" @click="handleCancelClick">取消</a-button>
                        <a-button :disabled="!canEdit" type="primary" @click="handleCommitClick">
                            保存
                        </a-button>
                    </a-space>
                </a-col>
            </a-row> -->

            <!-- 选择槽号对话框 -->
            <a-modal v-model:visible="showAssignPots" title="选择槽号" width="70vw" @ok="onModalConfirm"
                @cancel="onModalCancel" :confirm-loading="assignPotsLoading">
                <PotTable :area-id="props.data.id" view-mode="ASSIGN" @selected-keys="getSelectedPotKeys" />
            </a-modal>
            <div class="j-box-bottom-button offset-20" style="margin-top: 30px">
                <div class="j-box-bottom-button-float" :class="[`${prefixCls}`]">
                    <a-button preIcon="ant-design:sync-outlined" @click="onReset">重置</a-button>
                    <a-button type="primary" preIcon="ant-design:save-filled" @click="onSubmit">保存</a-button>
                </div>
            </div>
        </a-spin>
    </a-card>
</template>

<script lang="ts" setup>
import { watch, computed, ref, unref, onMounted } from 'vue';
import { BasicForm, useForm } from '/@/components/Form/index';
import { useBasicFormSchema } from '../orgManage.data';
import { queryOrgType, queryDict, queryUsedPots, queryUserDepTrees, saveOrgs } from '@/views/business/orgManage/orgManage.api';
import { useDesign } from '/@/hooks/web/useDesign';
import { DisplayMode, OrgTreeNodeType, OrgType } from '@/views/business/orgManage/orgManage.data';
import PotTable from './PotTable.vue';
import TankList from './TankList.vue';
import _ from 'lodash-es';
import { message } from 'ant-design-vue';

const { prefixCls } = useDesign('j-depart-form-content');

const emit = defineEmits(['success']);
const props = defineProps({
    data: { type: Object, default: () => ({}) },
    rootTreeData: { type: Array, default: () => [] },
    displayMode: { type: Number, default: () => DisplayMode.DEFAULT },
});

const loading = ref<boolean>(false);
// 当前是否是更新模式
const isUpdate = ref<boolean>(true);
// 当前的弹窗数据
const model = ref<object>({});
// modal
const showAssignPots = ref<boolean>(false)
//
const displayMode = ref<DisplayMode>(props.displayMode)

const assignPotsLoading = ref<boolean>(false)

const usedPotsList = ref<any[]>([])


watch(
    () => props.displayMode,
    (val) => {
        displayMode.value = val
    }
)

onMounted(() => {
    // 禁用字段
    updateSchema([
        // { field: 'parentId', componentProps: { disabled: true } },
        { field: 'orgCode', componentProps: { disabled: true } },
    ]);
    // data 变化，重填表单
    watch(
        () => props.data,
        async () => {

            if (props.data.typeId === OrgType.CORP) {
                updateSchema([{ field: 'parentId', show: false }]);
            } else {
                updateSchema([{ field: 'parentId', show: true }]);
            }
            //如果类型为工区,则读取槽号
            if (props.displayMode === DisplayMode.AREA) {
                //读取已分配槽号
                getUsedPots();
            }

            let record = unref(props.data);
            if (typeof record !== 'object') {
                record = {};
            }
            model.value = record;
            await resetFields();
            await setFieldsValue({ ...record });
        },
        { deep: true, immediate: true }
    );
    // 更新 父部门 选项
    watch(
        () => props.rootTreeData,
        async () => {
            updateSchema([
                {
                    field: 'parentId',
                    componentProps: { treeData: generateFather() },
                },
            ]);
        },
        { deep: true, immediate: true }
    );

    watch(
        () => props.displayMode,
        (val) => {
            updateSchema([
                { field: 'seriesId', show: val === DisplayMode.FACTORY },
                { field: 'manufactorId', show: val === DisplayMode.AREA },
            ]);
        },
        {
            immediate: true
        }
    )
});


//注册表单
const [registerForm, { resetFields, getFieldsValue, setFieldsValue, validate, updateSchema }] = useForm({
    schemas: useBasicFormSchema().basicFormSchema,
    showActionButtonGroup: false,
});

const getUsedPots = () => {
    if (!props.data.id) return;

    queryUsedPots({ areaId: props.data.id }).then(res => {
        usedPotsList.value = res.map(item => ({
            id: String(item.id as number),
            name: item.code as string,
        }))
    })
}

const init = () => {
    Promise.all([
        queryOrgType(),
        queryDict({ typeName: '系列' }),
        queryDict({ typeName: '槽控厂家' }),
        queryUserDepTrees()
    ]).then(res => {
        const [typeOptions, seriesOptions, potSysOptions, userDepTreeOptions] = res
        updateSchema([
            {
                field: 'typeId',
                componentProps: {
                    options: typeOptions.map(r => ({
                        label: r.value,
                        value: r.id
                    }))
                },
            },
            {
                field: 'seriesId',
                componentProps: {
                    options: seriesOptions.map(r => ({
                        label: r.value,
                        value: r.id
                    }))
                },
            },
            {
                field: 'manufactorId',
                componentProps: {
                    options: potSysOptions.map(r => ({
                        label: r.value,
                        value: r.id
                    }))
                },
            },
            {
                field: 'departmentId',
                componentProps: {
                    treeData: userDepTreeOptions
                },
            },
        ]);
    })
}

init()

function generateFather() {
    const parentId = getFieldsValue().parentId || props.data.parentId
    if (!parentId) return props.rootTreeData;

    const tempFather = _.cloneDeep(props.rootTreeData);
    removeChildByKey(tempFather, parentId);
    return tempFather;
}

//去掉本级以下的部门作为上级部门
function removeChildByKey(treeData: OrgTreeNodeType[], key: string): void {
    if (!(treeData && treeData?.length > 0)) {
        return;
    }
    treeData.forEach((item) => {
        if (item.key == key) {
            item.children = [];
            return;
        } else {
            removeChildByKey(item.children, key);
        }
    });
}

const onSelectPots = () => {
    showAssignPots.value = true
}

const onModalConfirm = () => {
    message.info('未修改任何数据')
    showAssignPots.value = false;
    // setAssignPotsLoading(true);
    // // @ts-ignore
    // reSavePotsUsingPUT({ potIdList: selectedPotKeys, areaId: currentKey })
    //   .then((response) => {
    //     message.info('数据传输成功').then();
    //     getUsedPots(currentKey);
    //     showAssignPots.value = true
    //     setAssignPotsLoading(false);
    //   })
    //   .catch(() => {
    //     setAssignPotsLoading(false);
    //     showAssignPots.value = true
    //   });
}

const onModalCancel = () => {
    showAssignPots.value = false
}

const getSelectedPotKeys = () => {
    return []
}

// 重置表单
async function onReset() {
    await resetFields();
    await setFieldsValue({ ...model.value });
}

// 提交事件
async function onSubmit() {
    try {
        loading.value = true;
        let values = await validate();
        values = Object.assign({}, model.value, values);
        const data = getFieldsValue();
        //提交表单
        // const req = pick(values, ['name', 'nickname', 'typeId', 'departmentId', 'parentId'])
        //  {
        //     "name": "测试9区",
        //     "nickname": "测试9区",
        //     "typeId": "1052246450638946304",
        //     "departmentId": "1738003279505309698",
        //     "parentId": "1052246451428210001"
        // }
        await saveOrgs({ data: [data] });
        //刷新列表
        emit('success');
        Object.assign(model.value, values);
    } finally {
        loading.value = false;
    }
}
</script>
<style lang="less">
// update-begin-author:liusq date:20230625 for: [issues/563]暗色主题部分失效

@prefix-cls: ~'@{namespace}-j-depart-form-content';

/*begin 兼容暗夜模式*/
.@{prefix-cls} {
    background: @component-background;
    border-top: 1px solid @border-color-base;
}

/*end 兼容暗夜模式*/
// update-end-author:liusq date:20230625 for: [issues/563]暗色主题部分失效</style>