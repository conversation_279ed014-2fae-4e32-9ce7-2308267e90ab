import { defHttp } from '/@/utils/http/axios';

enum Api {
  loginfo = '/sys/loginfo',
  userNum = '/sys/userNum',
  visitInfo = '/sys/visitInfo',
  flowMax = '/sys/flowMax',
  flowInfo = '/sys/flowInfo',
}

/**
 * 在线用户信息
 * @param params
 */
export const getUserNumInfo = (params) => defHttp.get({ url: Api.userNum, params }, { isTransformResponse: false });

/**
 * 日志统计信息
 * @param params
 */
export const getLoginfo = (params) => defHttp.get({ url: Api.loginfo, params }, { isTransformResponse: false });

/**
 * 访问量信息
 * @param params
 */
export const getVisitInfo = (params) => defHttp.get({ url: Api.visitInfo, params }, { isTransformResponse: false });

/**
 * 流量值信息-当前小时
 * @param params
 */
export const getFlowMax = (params) => defHttp.get({ url: Api.flowMax, params }, { isTransformResponse: false });

/**
 * 流量值信息
 * @param params
 */
export const getFlowInfo = (params) => defHttp.get({ url: Api.flowInfo, params }, { isTransformResponse: false });
