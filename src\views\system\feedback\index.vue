<!--
 * @Description: 
 * @Autor: yst
 * @Date: 2024-10-22 15:51:49
 * @LastEditors: yst
 * @LastEditTime: 2024-10-24 16:14:45
-->
<!-- 换级工作 -->
<template>
  <BasicTable @register="registerTable">
    <template #picture="{ text }">
      <div style="width: 200px">
        <TableImage v-if="text[0]" :margin="10" :imgList="text" />
        <span v-else>{{ '-' }}</span>
      </div>
    </template>
    <template #date="{ text, record }">
      <div style="width: 200px" v-if="record.type == 2">
        <span>{{ text }}</span>
      </div>
      <div style="width: 200px" v-if="record.type != 2">
        <span>{{ '-' }}</span>
      </div>
    </template>
    <template #descr="{ text }">
      <div style="width: 200px">
        <span style="white-space: pre-wrap">{{ text }}</span>
      </div>
    </template>
    <template #type="{ text }"> {{ text == 1 ? '建议' : '问题' }}</template>
    <template #tableTitle>
      <a-button type="primary" preIcon="ant-design:export-outlined" @click="handleDownload"> 导出</a-button>
    </template>
  </BasicTable>
</template>

<script setup lang="ts">
  import { BasicTable } from '/@/components/Table';
  import TableImage from '/@/components/Table/src/components/TableImg.vue';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, searchFormSchema } from './index.data';
  import { list, exportXlsApi, getUserDepartList, getUserDeparts } from './index.api';
  import { useMethods } from '@/hooks/system/useMethods';
  import { filterObj } from '@/utils/common/compUtils';
  import { onMounted } from 'vue';

  const { tableContext } = useListPage({
    designScope: 'basic-table-demo-filter',
    tableProps: {
      title: '',
      api: list,
      beforeFetch(params) {
        const arr = params.startEndDate.split(',');
        params.startTime = arr[0] + ` 00:00:00`;
        params.endTime = arr[1] + ` 23:59:59`;
        getParams(params);
      },
      afterFetch: (data) => {
        const res = data || [];
        res.forEach((i) => (i.picture = i.picture.split(',')));
        console.log(res, 'ddd');
        return res;
      },
      columns: columns,
      striped: true,
      formConfig: {
        schemas: searchFormSchema,
        showAdvancedButton: false,
      },
      showActionColumn: false,
    },
  });
  onMounted(() => {
    init();
  });

  const init = async () => {
    const res = await getUserDeparts();
    console.log(res);
    const orgCode = res.orgCode;
    await getForm().setFieldsValue({
      sysOrgCode: orgCode,
    });
    const schemas: any = [
      {
        label: '部门',
        field: 'sysOrgCode',
        component: 'JSelectDept',
        defaultValue: orgCode,
        componentProps: {
          multiple: false,
          labelKey: 'departName',
          rowKey: 'orgCode',
          maxTagCount: 1,
          checkable: false,
          showButton: false,
          mode: 'combobox',
        },
        colProps: { span: 6 },
      },
    ];
    getForm().updateSchema(schemas);
    reload();
  };
  //BasicTable绑定注册
  const [registerTable, { reload, getForm }] = tableContext;

  let downList;
  const getParams = (params) => {
    downList = params;
  };

  const { handleExportXls } = useMethods();
  const handleDownload = async () => {
    handleExportXls(`反馈列表__`, exportXlsApi, filterObj(downList), true);
  };
</script>

<style scoped></style>
