<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" :disabled="selectedRowKeys.length <= 0" @click="mulDeal"> 批量处理</a-button>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="system-user" setup>
  //ts语法
  import { ref, computed, unref } from 'vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema } from './data';
  import { list,batchUpdateStatus } from './api';

  const { createMessage, createConfirm } = useMessage();

  // 列表页面公共参数、方法
  const { prefixCls, tableContext, } = useListPage({
    designScope: 'event-log-list',
    tableProps: {
      title: '用户列表',
      api: list,
      columns: columns,
      size: 'small',
      showActionColumn: false,
      formConfig: {
        // labelWidth: 200,
        schemas: searchFormSchema,
      },
      // beforeFetch: (params) => {
      //   return Object.assign({ column: 'createTime', order: 'desc' }, params);
      // },
    },
  });

  //注册table数据
  const [registerTable, { reload, updateTableDataRecord }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;

  const mulDeal = async () => {
    createConfirm({
      iconType: 'warning',
      title: '确认操作',
      content: '是否处理选中事件?',
      onOk: async () => {
        await batchUpdateStatus({ ids: selectedRowKeys.value });
        selectedRowKeys.value = [];
        reload();
      },
    });
  };
</script>

<style scoped></style>
