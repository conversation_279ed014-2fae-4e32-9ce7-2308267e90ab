<template>
    <a-card :bordered="false" style="height: 100%">
        <a-spin :spinning="loading">
            <!-- <a-input-search placeholder="按组织名称搜索…" style="margin-bottom: 10px" @search="onSearch" /> -->
            <!--组织机构树-->
            <template v-if="treeData.length > 0">
                <a-tree 
                    v-if="!treeReloading" 
                    v-model:expandedKeys="expandedKeys" 
                    :clickRowToExpand="false" 
                    :treeData="treeData" 
                    :field-names="{
                        children: 'childs',
                        label: 'name',
                        value: 'id',
                    }" 
                    :selectedKeys="selectedKeys" 
                    :checkStrictly="checkStrictly" 
                    :checkedKeys="checkedKeys"
                    :treeLine="true" 
                    @check="onCheck" 
                    @select="onSelect"
                >
                    <template #title="{ id: treeKey, name: title, dataRef }">
                        <a-dropdown :trigger="['contextmenu']">
                            
                            <span>{{ title }}</span>

                            <template #overlay>
                                <a-menu @click="">
                                    <a-menu-item key="1" v-if="dataRef.typeId !== OrgType.AREA"
                                        @click="onAddChildDepart(dataRef)">添加子级</a-menu-item>
                                    <a-menu-item key="2" v-if="dataRef.typeId === OrgType.AREA"
                                        @click="onDelete()">
                                        <a-popconfirm 
                                            v-model="visiblePop" 
                                            title="确定要删除吗？" 
                                            ok-text="确定"
                                            cancel-text="取消" 
                                            placement="rightTop" 
                                            :disabled="false"
                                            @confirm="onDeleteConfirm(dataRef)"
                                        >
                                        <span style="color: red">删除</span>
                                    </a-popconfirm>
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </template>
                </a-tree>
            </template>
            <a-empty v-else description="暂无数据" />
        </a-spin>
    </a-card>
</template>
<script lang="ts" setup>
import { ref, nextTick, unref, watch } from 'vue'
import { searchByKeywords } from '/@/views/system/departUser/depart.user.api';
import { queryOrg, deleteOrgs } from '@/views/business/orgManage/orgManage.api';
import { DisplayMode, OrgType } from '@/views/business/orgManage/orgManage.data';
import { useMessage } from '/@/hooks/web/useMessage';


const emit = defineEmits(['select', 'rootTreeData']);
const { createMessage } = useMessage();


const loading = ref<boolean>(false);
// 部门树列表数据
const treeData = ref<any[]>([]);
// 当前选中的项
const checkedKeys = ref<any[]>([]);
// 当前展开的项
const expandedKeys = ref<any[]>([]);
// 当前选中的项
const selectedKeys = ref<any[]>([]);
// 树组件重新加载
const treeReloading = ref<boolean>(false);
// 树父子是否关联
const checkStrictly = ref<boolean>(false);
// 当前选中的部门
const currentDepart = ref<any>(null);
// 控制确认删除提示框是否显示
const visiblePop = ref<boolean>(false);
// 搜索关键字
const searchKeyword = ref('');


// 自动展开父节点，只展开一级
function autoExpandParentNode() {
    let item = treeData.value[0];
    if (item) {
        if (!item.isLeaf) {
            expandedKeys.value = [item.id];
        }
        // 默认选中第一个
        setSelectedKey(item.id, item);
        reloadTree();
    } else {
        emit('select', null);
    }
}

// 重新加载树组件，防止无法默认展开数据
async function reloadTree() {
    await nextTick();
    treeReloading.value = true;
    await nextTick();
    treeReloading.value = false;
}

/**
 * 设置当前选中的行
 */
function setSelectedKey(key: string, data?: object) {
    selectedKeys.value = [key];
    if (data) {
        let displayModel = DisplayMode.DEFAULT;
        switch (data.typeId) {
            case OrgType.CORP:
            case OrgType.WORKSHOP:
                displayModel = DisplayMode.DEFAULT;
                break;
            case OrgType.FACTORY:
                displayModel = DisplayMode.FACTORY;
                break;
            case OrgType.AREA:
                displayModel = DisplayMode.AREA;
                break;
            default:
                displayModel = DisplayMode.DEFAULT;
        }
        currentDepart.value = data;
        emit('select', data, displayModel);
    }
}

const getOrgList = () => {
    loading.value = true;
    treeData.value = [];
    queryOrg().then(result => {
        if (Array.isArray(result)) {
            treeData.value = result.map(r => {
                return {
                    ...r,
                    isLeaf: r.childs.length === 0,
                    key: r.id,
                }
            });
        }
        if (expandedKeys.value.length === 0) {
            autoExpandParentNode();
        } else {
            if (selectedKeys.value.length === 0) {
                let item = treeData.value[0];
                if (item) {
                    // 默认选中第一个
                    setSelectedKey(item.id, item);
                }
            } else {
                emit('select', currentDepart.value, DisplayMode.DEFAULT);
            }
        }
        emit('rootTreeData', treeData.value);
    }).finally(() => loading.value = false)
}


getOrgList()


watch(() => expandedKeys.value, () => {
    console.log('ex:', expandedKeys.value)
})

// 加载顶级部门信息
async function loadRootTreeData() {
    getOrgList()
}


// 加载子级部门信息
// async function loadChildrenTreeData(treeNode) {
//     try {
//         const result = await queryDepartTreeSync({
//             pid: treeNode.dataRef.id,
//         });
//         if (result.length == 0) {
//             treeNode.dataRef.isLeaf = true;
//         } else {
//             treeNode.dataRef.children = result;
//             if (expandedKeys.value.length > 0) {
//                 // 判断获取的子级是否有当前展开的项
//                 let subKeys: any[] = [];
//                 for (let key of expandedKeys.value) {
//                     if (result.findIndex((item) => item.id === key) !== -1) {
//                         subKeys.push(key);
//                     }
//                 }
//                 if (subKeys.length > 0) {
//                     expandedKeys.value = [...expandedKeys.value];
//                 }
//             }
//         }
//         treeData.value = [...treeData.value];
//         emit('rootTreeData', treeData.value);
//     } catch (e) {
//         console.error(e);
//     }
//     return Promise.resolve();
// }

// 添加子级部门
function onAddChildDepart(data = currentDepart.value) {
    if (data == null) {
        createMessage.warning('请先选择一个部门');
        return;
    }
    const record = {
        id: '',
        name: '',
        parentId: data.id,
        typeId: data.typeId === OrgType.CORP ? OrgType.FACTORY : OrgType.AREA,
        seriesId: '',
        nickname: '',
        manufactorId: '',
        departmentId: ''
    };
    // currentDepart.value = Object.assign(data, record); 
    const model = data.typeId === OrgType.CORP ? DisplayMode.FACTORY : DisplayMode.AREA
    emit('select', record, model)
    // openModal(true, { isUpdate: false, isChild: true, record });
}


// 搜索事件
async function onSearch(value: string) {
    if (value) {
        try {
            loading.value = true;
            treeData.value = [];
            let result = await searchByKeywords({ keyWord: value });
            if (Array.isArray(result)) {
                treeData.value = result;
            }
            autoExpandParentNode();
        } finally {
            loading.value = false;
        }
    } else {
        loadRootTreeData();
    }
    searchKeyword.value = value;
}

// 树复选框选择事件
function onCheck(e) {
    if (Array.isArray(e)) {
        checkedKeys.value = e;
    } else {
        checkedKeys.value = e.checked;
    }
}

// 树选择事件
function onSelect(selKeys, event) {
    if (selKeys.length > 0 && selectedKeys.value[0] !== selKeys[0]) {
        setSelectedKey(selKeys[0], event.selectedNodes[0]);
    } else {
        // 这样可以防止用户取消选择
        setSelectedKey(selectedKeys.value[0]);
    }
}

/**
* 根据 ids 删除部门
* @param idListRef array
* @param confirm 是否显示确认提示框
*/
async function doDeleteDepart(idListRef) {
    const idList = unref(idListRef);
    if (idList.length > 0) {
        try {
            loading.value = true;
            await deleteOrgs({ data: [{ id: idListRef[0] }] });
            expandedKeys.value = []
            await loadRootTreeData();
        } finally {
            loading.value = false;
        }
    }
}
// 删除确认
async function onDeleteConfirm(data) {
    if (data) {
        doDeleteDepart([data.id]);
    }
}

// 删除
function onDelete() {
    visiblePop.value = true
}


</script>