import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/system/sysAppVersionManage/list',
  save = '/system/sysAppVersionManage/add',
  edit = '/system/sysAppVersionManage/edit',
  deleteOne = '/system/sysAppVersionManage/delete',
  deleteBatch = '/system/sysAppVersionManage/deleteBatch',
  importExcel = '/system/sysAppVersionManage/importExcel',
  exportXls = '/system/sysAppVersionManage/exportXls',
  appDown = '/system/sysAppVersionManage/down/',
  appUp = '/system/sysAppVersionManage/up/',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 上架
 */
export const appUp = (params, handleSuccess) => {
  const { id } = params;
  return defHttp.put({ url: Api.appUp + id }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 下架
 */
export const appDown = (params, handleSuccess) => {
  const { id } = params;
  return defHttp.put({ url: Api.appDown + id }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};
