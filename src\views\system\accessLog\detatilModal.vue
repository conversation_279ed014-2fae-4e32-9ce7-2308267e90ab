<script setup lang="ts">
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable } from '@/components/Table';
  import { useListPage } from '@/hooks/system/useListPage';
  import { findVisitColumns, findNotVisitColumns } from '@/views/system/accessLog/detailModal.data';
  import { findNotVisitList, findVisitList, exportVisitListUrl, exportNotVisitListUrl } from './index.api';
  import { DownloadOutlined } from '@ant-design/icons-vue';
  import { ref } from 'vue';
  import { useMethods } from '/@/hooks/system/useMethods';
  import dayjs from "dayjs";

  const { handleExportXls } = useMethods();
  let requestParams = {};
  let title = ref('');
  let exportExtendParams = {};
  let isVisit = false;
  const [register, { setModalProps, closeModal }] = useModalInner((data) => {
    setProps({
      api: data.isVisit ? findVisitList : findNotVisitList,
      columns: data.isVisit ? findVisitColumns : findNotVisitColumns,
    });

    requestParams = {};
    exportExtendParams = {
      appName: data.record.appName,
    };

    if (data.dateScopeData) {
      requestParams.startTime = data.dateScopeData.startTime;
      requestParams.endTime = data.dateScopeData.endTime;
    }
    requestParams.permissionId = data.record.permissionId;

    if (!data.isVisit) {
      title.value = '未访问明细';
      requestParams.permissionName = data.record.permissionName;
    } else {
      title.value = '访问明细';
      exportExtendParams.permissionName = data.record.permissionName;
    }

    isVisit = data.isVisit;

    reload();
  });

  const { tableContext } = useListPage({
    tableProps: {
      immediate: false,
      columns: [],
      useSearchForm: false,
      showTableSetting: false,
      actionColumn: false,
      beforeFetch: (params) => {
        delete params.column;
        delete params.order;

        for (let i in requestParams) {
          params[i] = requestParams[i];
        }
      },
      afterFetch: (list) => {
        if (!isVisit) {
          list.forEach((item) => {
            item.visitTime = dayjs(item.visitTime).format('YYYY-MM-DD');
          });
        }
      },
    },
  });

  const [registerTable, { reload, setProps }] = tableContext;

  function ok() {
    closeModal();
  }

  function downloadXls() {
    const url = isVisit ? exportVisitListUrl : exportNotVisitListUrl;
    const params = {
      ...requestParams,
      ...exportExtendParams,
    };

    handleExportXls(title.value, url, params);
  }
</script>

<template>
  <BasicModal
    :width="1300"
    @register="register"
    destroyOnClose
    v-bind="$attrs"
    @ok="ok"
    :canFullscreen="true"
    okText="关闭"
    :centered="true"
    :show-cancel-btn="false"
  >
    <template #title>
      <div class="title-wrap">
        <div class="title">{{ title }}</div>
        <div class="menu">
          <a-button type="primary" size="small" @click="downloadXls">
            <template #icon>
              <DownloadOutlined />
            </template>
            下载
          </a-button>
        </div>
      </div>
    </template>
    <BasicTable @register="registerTable"></BasicTable>
  </BasicModal>
</template>

<style scoped lang="less">
  .title-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-right: 70px;
  }
</style>
