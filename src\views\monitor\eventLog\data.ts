import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
export const columns: BasicColumn[] = [
  {
    title: '事件ID',
    dataIndex: 'eventId',
    width: 250,
  },
  {
    title: '事件內容',
    dataIndex: 'eventData',
    width: 120,
  },
  {
    title: '事件类型',
    dataIndex: 'eventType',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(text, 'dingtalk_event_type');
    },
  },
  {
    title: '处理状态',
    dataIndex: 'handlerType',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(text, 'dingtalk_event_handler');
    },
  },
  // {
  //   title: '创建人',
  //   dataIndex: 'createBy',
  //   width: 120,
  // },
  {
    title: '创建时间',
    width: 180,
    dataIndex: 'createTime',
  },
  {
    title: '发生时间',
    width: 180,
    dataIndex: 'eventBornTime',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '事件类型',
    field: 'eventType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'dingtalk_event_type',
    },
    colProps: { span: 8 },
  },
  {
    label: '处理状态',
    field: 'handlerType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'dingtalk_event_handler',
    },
    colProps: { span: 8 },
  },
  {
    label: '事件触发时间',
    field: 'eventBornTime_begin',
    component: 'DatePicker',
    componentProps: {
      style: { width: '100%' },
      valueType: 'Date',
    },
    colProps: { span: 8 },
  },
  {
    label: '事件结束时间',
    field: 'eventBornTime_end',
    component: 'DatePicker',
    componentProps: {
      style: { width: '100%' },
      valueType: 'Date',
    },
    colProps: { span: 8 },
  },
];
