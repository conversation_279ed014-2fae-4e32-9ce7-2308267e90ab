import { FormSchema } from '/@/components/Form';

// 部门基础表单
export function useBasicFormSchema() {
  const basicFormSchema: FormSchema[] = [
    {
      field: 'id',
      label: 'id',
      component: 'Input',
      show: false
    },
    {
      field: 'name',
      label: '名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入名称',
      },
      rules: [
        { required: true, message: '名称不能为空' },
        {
          type: 'string',
          max: 30,
          message: '名称超长,请输入少于30个字的组织名称',
        },
      ],
    },
    {
      field: 'parentId',
      label: '上级机构',
      component: 'TreeSelect',
      componentProps: {
        treeData: [],
        placeholder: '无',
        dropdownStyle: { maxHeight: '200px', overflow: 'auto' },
        fieldNames: {
          children:'childs', 
          label:'name', 
          value: 'id' 
        }
      },
      rules: [{ required: true, message: '机构不能为空' }],
    },
    {
      field: 'typeId',
      label: '组织类型',
      component: 'RadioGroup',
      componentProps: { options: [] },
      rules: [{ required: true, message: '组织类型不能为空' }],
    },
    {
      field: 'seriesId',
      label: '系列',
      component: 'RadioGroup',
      componentProps: { options: [] },
      rules: [{ required: true, message: '系列不能为空' }],
    },
    {
      field: 'nickname',
      label: '简称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入简称',
      },
      rules: [{ type: 'string', max: 30, message: '请输入少于30个字的简称' }],
    },
    {
      field: 'manufactorId',
      label: '槽控系统',
      component: 'RadioGroup',
      componentProps: { options: [] },
      rules: [{ required: true, message: '槽控系统不能为空' }],
    },
    {
      field: 'departmentId',
      label: '组织绑定',
      component: 'TreeSelect',
      componentProps: {
        treeData: [],
        placeholder: '无',
        dropdownStyle: { maxHeight: '200px', overflow: 'auto' },
      },
      rules: [{ required: true, message: '组织绑定不能为空' }],
    },
  ];
  return { basicFormSchema };
}


//不同组织类型的显示模式
export enum DisplayMode {
  //默认模式
  DEFAULT,
  //分厂模式
  FACTORY,
  //工区模式
  AREA,
}

//定义组织类型
export const OrgType = {
  //分公司
  CORP: '1052246450513117184',
  //分厂
  FACTORY: '1052246450559254528',
  //厂房
  WORKSHOP: '1052246450597003264',
  //工区
  AREA: '1052246450638946304',
};