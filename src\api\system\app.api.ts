import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';
import { Api } from '/@/views/system/depart/depart.api';

const { createConfirm } = useMessage();

enum AppApi {
  list = '/sys/app/list',
  all = '/sys/app/all',
  myAll = '/sys/app/myAll',
  departAll = '/sys/app/depart/all',
  save = '/sys/app',
  edit = '/sys/app',
  deleteOne = '/sys/app',
  deleteBatch = '/sys/app/deleteBatch',
  importExcel = '/sys/app/importExcel',
  exportXls = '/sys/app/exportXls',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = AppApi.exportXls;
/**
 * 导入api
 */
export const getImportUrl = AppApi.importExcel;
/**
 * 列表接口
 * @param params
 */
export const getAppList = (params) => defHttp.get({ url: AppApi.list, params });

/**
 * 集合接口
 * @param params
 */
export const getAppAll = (params) => defHttp.get({ url: AppApi.all, params });

/**
 * 获取有权限应用集合接口
 * @param params
 */
export const queryMyAppAll = (params) => defHttp.get({ url: AppApi.myAll, params });

/**
 * 获取部门权限应用集合接口-部门
 * @param params
 */
export const queryDepartAppAll = (params) => defHttp.get({ url: AppApi.departAll, params });

/**
 * 删除单个
 */
export const deleteOneApp = (params, handleSuccess) => {
  let { id } = params;
  return defHttp.delete({ url: `${AppApi.deleteOne}/${id}` }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDeleteApp = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: AppApi.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdateApp = (params, isUpdate) => {
  return isUpdate ? defHttp.put({ url: AppApi.edit, params }) : defHttp.post({ url: AppApi.save, params });
};
