import { defHttp } from '/@/utils/http/axios';

export enum Api {
  list = '/sys/sysPermissionVisitLog/list',
  findVisitList = '/sys/sysPermissionVisitLog/findVisitList',
  findNotVisitList = '/sys/sysPermissionVisitLog/findNotVisitList',
  appList = '/sys/app/getAppSelectData',
  exportVisitList = '/sys/sysPermissionVisitLog/exportVisitList',
  exportNotVisitList = '/sys/sysPermissionVisitLog/exportNotVisitList',
}

/**
 * 部门用户信息
 */
export const list = (params?) => defHttp.get({ url: Api.list, params });

export const findVisitList = (params?) => defHttp.get({ url: Api.findVisitList, params });

export const findNotVisitList = (params?) => defHttp.get({ url: Api.findNotVisitList, params });

/**
 * 获取应用列表筛选项
 */
export const appList = () => defHttp.get({ url: Api.appList });

export const exportVisitListUrl = Api.exportVisitList;

export const exportNotVisitListUrl = Api.exportNotVisitList;
