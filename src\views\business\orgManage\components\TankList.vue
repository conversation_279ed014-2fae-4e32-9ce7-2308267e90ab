<template>
    <a-row>
        <a-col :span="20">
            <a-space wrap :size="'small'">
                <div v-for="item in tankListData" :key="item.id" :style="{
                    width: '40px',
                    height: '20px',
                    backgroundColor: 'lightblue',
                    textAlign: 'center',
                    lineHeight: '20px',
                }">
                    {{ item.name }}
                </div>
            </a-space>
        </a-col>
    </a-row>
</template>

<script>
import { defineComponent } from 'vue';
import { Row, Col, Space } from 'ant-design-vue';

export default defineComponent({
    components: {
        ARow: Row,
        ACol: Col,
        ASpace: Space,
    },
    props: {
        tankListData: {
            type: Array,
            required: true,
        },
    },
});
</script>