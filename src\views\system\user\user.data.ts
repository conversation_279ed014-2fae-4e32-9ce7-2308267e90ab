import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getAllRolesListNoByTenant, getAllTenantList } from './user.api';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
export const columns: BasicColumn[] = [
  {
    title: '用户账号',
    dataIndex: 'username',
    width: 120,
  },
  {
    title: '用户姓名',
    dataIndex: 'realname',
    width: 100,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 100,
  },
  {
    title: '工号',
    dataIndex: 'workNo',
    width: 100,
  },
  {
    title: '部门',
    width: 300,
    dataIndex: 'orgCodeTxt',
  },
  {
    title: '部门路径',
    width: 550,
    dataIndex: 'orgCodeLevelTxt',
  },
  {
    title: '负责部门',
    width: 150,
    dataIndex: 'departIds_dictText',
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 80,
    sorter: true,
    customRender: ({ text }) => {
      return render.renderDict(text, 'sex');
    },
  },
  {
    title: '生日',
    dataIndex: 'birthday',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status_dictText',
    width: 80,
  },
  {
    title: '头像',
    dataIndex: 'avatar',
    width: 120,
    customRender: render.renderAvatar,
  },
];

export const recycleColumns: BasicColumn[] = [
  {
    title: '用户账号',
    dataIndex: 'username',
    width: 100,
  },
  {
    title: '用户姓名',
    dataIndex: 'realname',
    width: 100,
  },
  {
    title: '头像',
    dataIndex: 'avatar',
    width: 80,
    customRender: render.renderAvatar,
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 80,
    sorter: true,
    customRender: ({ text }) => {
      return render.renderDict(text, 'sex');
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '账号',
    field: 'username',
    component: 'JInput',
    colProps: { span: 5 },
  },
  {
    label: '姓名',
    field: 'realname',
    component: 'JInput',
    colProps: { span: 5 },
  },
  {
    label: '工号',
    field: 'workNo',
    component: 'JInput',
    colProps: { span: 6 },
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sex',
      placeholder: '请选择性别',
      stringToNumber: true,
    },
    colProps: { span: 3 },
  },
  {
    label: '手机号码',
    field: 'phone',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '部门',
    field: 'depId',
    component: 'JSelectDept',
    componentProps: {
      multiple: false,
      maxTagCount: 1,
      checkable: false,
      showButton: false,
      mode: 'combobox',
    },
    colProps: { span: 6 },
  },
  {
    label: '用户状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'user_status',
      placeholder: '请选择状态',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '用户账号',
    field: 'username',
    component: 'Input',
    // required: true,
    dynamicDisabled: ({ values }) => {
      return !!values.id;
    },
    // dynamicRules: ({ model, schema }) => {
    //   let rulesData = [{ required: true }];

    //   return [...rulesData, rules.duplicateCheckRule('sys_user', 'username', model, schema, true)];
    // },
    dynamicRules: ({ model, schema }) => {
      const data = [{ ...rules.duplicateCheckRule('sys_user', 'username', model, schema, true)[0], required: true }];
      const rulesData = [{ ...rules.rule('username', false) }];
      return [...data, ...rulesData];
    },
  },
  {
    label: '登录密码',
    field: 'password',
    component: 'InputPassword',

    helpMessage: ['密码强度校验6-20位,', '①至少一个大写字母、', '②至少一个小写字母、', '③至少一个数字'],

    rules: [
      {
        pattern: /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])\S*$/,
        message: '请按要求填写密码',
        required: true,
      },
    ],

    componentProps: ({ formModel }) => {
      return {
        isAdvanced: true,
        onPaste: (e) => {
          e.preventDefault();
        },

        placeholder: '请输入登录密码',

        maxlength: 20,
      };
    },
  },
  {
    label: '确认密码',
    field: 'confirmPassword',
    component: 'InputPassword',
    dynamicRules: ({ values }) => rules.confirmPassword(values, true),
    componentProps: ({ formModel }) => {
      return {
        isAdvanced: true,
        onPaste: (e) => {
          e.preventDefault();
        },
      };
    },
  },
  {
    label: '用户姓名',
    field: 'realname',
    required: true,
    component: 'Input',
  },
  {
    label: '工号',
    field: 'workNo',
    component: 'Input',
    // dynamicRules: ({ model, schema }) => {
    //   let data = [{ ...rules.duplicateCheckRule('sys_user', 'work_no', model, schema, true)[0], required: true }];
    //   let rulesData = [{ ...rules.rule('work_no', false) }];
    //   return [...data, ...rulesData];
    // },
  },
  {
    label: '职务',
    field: 'post',
    required: false,
    component: 'JSelectPosition',
    componentProps: {
      rowKey: 'code',
      labelKey: 'name',
    },
  },
  {
    label: '角色',
    field: 'selectedroles',
    component: 'ApiSelect',
    componentProps: {
      mode: 'multiple',
      api: getAllRolesListNoByTenant,
      labelField: 'roleName',
      valueField: 'id',
    },
  },
  {
    label: '所属部门',
    field: 'selecteddeparts',
    component: 'JSelectDept',
    required: true,
    componentProps: ({ formActionType, formModel }) => {
      return {
        sync: false,
        checkStrictly: true,
        defaultExpandLevel: 2,

        onSelect: (options, values) => {
          const { updateSchema } = formActionType;
          //所属部门修改后更新负责部门下拉框数据
          updateSchema([
            {
              field: 'departIds',

              componentProps: { options },
            },
          ]);
          //所属部门修改后更新负责部门数据
          formModel.departIds && (formModel.departIds = formModel.departIds.filter((item) => values.value.indexOf(item) > -1));
        },
        onChange: (value) => {
          const { updateSchema } = formActionType;

          if (value.length == 0) {
            updateSchema([
              {
                field: 'departIds',

                componentProps: { options: [] },
              },
            ]);
            formModel.departIds && (formModel.departIds = []);
          }
        },
      };
    },
  },
  {
    label: '租户',
    field: 'relTenantIds',
    component: 'ApiSelect',
    componentProps: {
      mode: 'multiple',
      api: getAllTenantList,
      numberToString: true,
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    label: '身份',
    field: 'userIdentity',
    component: 'RadioGroup',
    defaultValue: 1,
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '普通用户', value: 1, key: '1' },
          { label: '上级', value: 2, key: '2' },
        ],
        onChange: () => {
          formModel.userIdentity == 1 && (formModel.departIds = []);
        },
      };
    },
  },
  {
    label: '负责部门',
    field: 'departIds',
    component: 'Select',
    componentProps: {
      mode: 'multiple',
    },
    ifShow: ({ values }) => values.userIdentity == 2,
  },
  {
    label: '头像',
    field: 'avatar',
    component: 'JImageUpload',
    componentProps: {
      fileMax: 1,
    },
  },
  {
    label: '生日',
    field: 'birthday',
    component: 'DatePicker',
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sex',
      placeholder: '请选择性别',
      stringToNumber: true,
    },
  },
  {
    label: '邮箱',
    field: 'email',
    component: 'Input',

    // dynamicRules: ({ model, schema }) => {
    //   let data = [{ ...rules.duplicateCheckRule('sys_user', 'email', model, schema, true)[0] }];
    //   let rulesdata = [{ ...rules.rule('email', false)[0], required: true }];
    //   return [...data, ...rulesdata];
    // },
  },
  {
    label: '手机号码',
    field: 'phone',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      const rule = [];
      const rulesData = [{ ...rules.duplicateCheckRule('sys_user', 'phone', model, schema, true)[0], required: true }];
      return [...rulesData, { pattern: /^1[3456789]\d{9}$/ }];
    },
  },
  {
    label: '座机',
    field: 'telephone',
    component: 'Input',
    rules: [{ pattern: /^0\d{2,3}-[1-9]\d{6,7}$/, message: '请输入正确的座机号码' }],
  },
  // {
  //   label: '工作流引擎',
  //   field: 'activitiSync',
  //   defaultValue: 1,
  //   component: 'JDictSelectTag',
  //   componentProps: {
  //     dictCode: 'activiti_sync',
  //     type: 'radio',
  //     stringToNumber: true,
  //   },
  // },
];

export const formPasswordSchema: FormSchema[] = [
  {
    label: '用户账号',
    field: 'username',
    component: 'Input',

    componentProps: { readOnly: true },
  },
  {
    label: '登录密码',
    field: 'password',

    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入登录密码',
      maxlength: 20,
    },
    helpMessage: ['密码强度校验6-20位,', '①至少一个大写字母、', '②至少一个小写字母、', '③至少一个数字'],

    rules: [
      {
        pattern: /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])\S*$/,
        message: '请按要求填写密码',
        required: true,
      },
    ],
    // rules: [
    //   {
    //     required: true,
    //     message: '请输入登录密码',
    //   },
    // ],
  },
  {
    label: '确认密码',
    field: 'confirmPassword',
    component: 'InputPassword',
    dynamicRules: ({ values }) => rules.confirmPassword(values, true),
  },
];

export const formAgentSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'userName',
    label: '用户名',
    component: 'Input',
    componentProps: {
      readOnly: true,
      allowClear: false,
    },
  },
  {
    field: 'agentUserName',
    label: '代理人用户名',
    required: true,
    component: 'JSelectUser',
    componentProps: {
      rowKey: 'username',
      labelKey: 'realname',
      maxSelectCount: 10,
    },
  },
  {
    field: 'startTime',
    label: '代理开始时间',
    component: 'DatePicker',
    required: true,
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择代理开始时间',
    },
  },
  {
    field: 'endTime',
    label: '代理结束时间',
    component: 'DatePicker',
    required: true,
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择代理结束时间',
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    defaultValue: '1',
    componentProps: {
      dictCode: 'valid_status',
      type: 'radioButton',
    },
  },
];

export const formQuitAgentSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'userName',
    label: '用户名',
    component: 'Input',
    componentProps: {
      readOnly: true,
      allowClear: false,
    },
  },
  {
    field: 'agentUserName',
    label: '交接人员',
    required: true,
    component: 'JSelectUser',
    componentProps: {
      rowKey: 'username',
      labelKey: 'realname',
      maxSelectCount: 1,
    },
  },
  {
    field: 'startTime',
    label: '交接开始时间',
    component: 'DatePicker',
    required: true,
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择交接开始时间',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'endTime',
    label: '交接结束时间',
    component: 'DatePicker',
    required: true,
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择交接结束时间',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    defaultValue: '1',
    componentProps: {
      dictCode: 'valid_status',
      type: 'radioButton',
    },
  },
];

//租户用户列表
export const userTenantColumns: BasicColumn[] = [
  {
    title: '用户账号',
    dataIndex: 'username',
    width: 120,
  },
  {
    title: '用户姓名',
    dataIndex: 'realname',
    width: 100,
  },
  {
    title: '头像',
    dataIndex: 'avatar',
    width: 120,
    customRender: render.renderAvatar,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 100,
  },
  {
    title: '部门',
    width: 150,
    dataIndex: 'orgCodeTxt',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      if (text === '1') {
        return '正常';
      } else if (text === '3') {
        return '审批中';
      } else {
        return '已拒绝';
      }
    },
  },
];

//用户租户搜索表单
export const userTenantFormSchema: FormSchema[] = [
  {
    label: '账号',
    field: 'username',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '姓名',
    field: 'realname',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sex',
      placeholder: '请选择性别',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
];

/**
 * 用户搜索form
 */
export const searchUserFormSchema: FormSchema[] = [
  {
    field: 'username',
    label: '用户账号',
    component: 'JInput',
    colProps: { span: 8 },
  },
  {
    field: 'realname',
    label: '用户姓名',
    component: 'JInput',
    colProps: { span: 8 },
  },
];

/**
 * 用户Columns
 */
export const userColumns = [
  {
    title: '用户账号',
    dataIndex: 'username',
  },
  {
    title: '用户姓名',
    dataIndex: 'realname',
  },
  {
    title: '状态',
    dataIndex: 'status_dictText',
    width: 80,
  },
];
