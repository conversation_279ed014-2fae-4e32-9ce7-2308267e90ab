/*
 * @Description:
 * @Autor: yst
 * @Date: 2024-10-22 15:51:49
 * @LastEditors: yst
 * @LastEditTime: 2024-10-24 16:10:22
 */
import { BasicColumn } from '/@/components/Table';
function getFormattedDate() {
  const now = new Date();
  const year = now.getFullYear().toString(); // 获取年份的后两位
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 获取月份并补零
  const day = String(now.getDate()).padStart(2, '0'); // 获取日期并补零

  const startOfDay = `${year}-${month}-${day}`;
  const endOfDay = `${year}-${month}-${day}`;

  return `${startOfDay},${endOfDay}`;
}
console.log(getFormattedDate(), 'ddd');
export const columns: BasicColumn[] = [
  {
    title: '反馈入口',
    dataIndex: 'entrance',
    width: 80,
  },
  {
    title: '反馈类型',
    dataIndex: 'type',
    slots: { customRender: 'type' },
    width: 80,
  },
  {
    title: '描述',
    dataIndex: 'descr',
    slots: { customRender: 'descr' },
    width: 80,
  },
  {
    width: 80,
    title: '照片',
    dataIndex: 'picture',
    slots: { customRender: 'picture' },
  },
  {
    title: '问题时间',
    dataIndex: 'date',
    width: 80,
    slots: { customRender: 'date' },
  },
  {
    title: '反馈日期',
    dataIndex: 'createTime',
    width: 80,
  },
  {
    title: '反馈人',
    dataIndex: 'createByName',
    width: 80,
  },
];

export const searchFormSchema: any[] = [
  {
    label: '日期',
    field: 'startEndDate',
    component: 'RangeDate',
    defaultValue: getFormattedDate(),
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: '请选择开始日期',
      allowClear: false,
    },
    colProps: { span: 6 },
    labelWidth: '40px',
  },
  {
    label: '部门',
    field: 'sysOrgCode',
    component: 'JSelectDept',
    componentProps: {
      multiple: false,
      maxTagCount: 1,
      checkable: false,
      labelKey: 'departName',
      rowKey: 'orgCode',
      showButton: false,
      mode: 'combobox',
    },
    colProps: { span: 6 },
  },
  {
    field: 'type',
    component: 'Select',
    defaultValue: '',
    label: '反馈类型',
    colProps: { span: 6 },
    componentProps: {
      options: [
        {
          label: '全部',
          value: '',
          key: '',
        },
        {
          label: '建议',
          value: '1',
          key: '1',
        },
        {
          label: '问题',
          value: '2',
          key: '2',
        },
      ],
    },
  },
];
